browser_component:
  title: Recently Viewed | Leads | Salesf...
  url: ability-power-35702.lightning.force.com/lightning/o/Lead/list?filterName=Recent
content:
- type: application_layout
  platform: Salesforce Lightning
  elements:
  - type: navigation_sidebar
    position: left
    items:
    - type: nav_item
      text: Home
      icon: home
    - type: nav_item
      text: Contacts
      icon: contacts
    - type: nav_item
      text: Accounts
      icon: accounts
    - type: nav_item
      text: Sales
      icon: sales_chart
      state: active
    - type: nav_item
      text: Service
      icon: service_heart
    - type: nav_item
      text: Marketing
      icon: marketing_search
    - type: nav_item
      text: Commerce
      icon: commerce_cart
    - type: nav_item
      text: Generative Canvas
      icon: generative_canvas
    - type: nav_item
      text: Your Account
      icon: your_account
  - type: main_content_area
    header:
      type: application_header
      logo: Salesforce
      application_name: Sales
      navigation_tabs:
      - text: Leads
        state: active
        has_dropdown: true
      - text: Contacts
        state: inactive
        has_dropdown: true
      - text: Accounts
        state: inactive
        has_dropdown: true
      - text: Opportunities
        state: inactive
        has_dropdown: true
      - text: Products
        state: inactive
        has_dropdown: true
      - text: Price Books
        state: inactive
        has_dropdown: true
      - text: Calendar
        state: inactive
        has_dropdown: true
      - text: Analytics
        state: inactive
        has_dropdown: false
      global_search:
        type: search_bar
        placeholder: Search...
      user_actions:
      - type: icon_button
        label: Help
      - type: icon_button
        label: Settings
      - type: icon_button
        label: Notifications
      - type: user_profile_icon
        avatar: bear_mascot
    body:
      type: list_view_page
      state: loading
      header:
        object_type: Leads
        view_title: Recently Viewed
        icon: star_in_circle
      actions:
        primary_buttons:
        - type: button
          text: New
        - type: button
          text: Import
        - type: button
          text: Add to Campaign
        - type: button
          text: Send Email
        - type: button
          text: Change Owner
          has_dropdown: true
      list_controls:
        message: Refresh this list to view the latest data
        search_bar:
          type: search_input
          placeholder: Search this list...
        control_buttons:
        - type: icon_button
          label: List View Controls
        - type: icon_button
          label: Display As
        - type: icon_button
          label: Refresh
        - type: icon_button
          label: Edit
        - type: icon_button
          label: Charts
        - type: icon_button
          label: Filter
      content:
        type: loading_indicator
        style: spinner
        skeleton_layout:
          type: table_rows
          visible: true
  - type: footer_toolbar
    state: collapsed
    items:
    - type: collapsible_panel
      title: To Do List
