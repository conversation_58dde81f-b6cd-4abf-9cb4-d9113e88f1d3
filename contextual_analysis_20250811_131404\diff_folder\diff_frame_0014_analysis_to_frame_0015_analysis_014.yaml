dictionary_item_added:
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][0]['sub_fields'][0]['current_value']: --None--
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][0]['sub_fields'][0]['options']:
  - text: --None--
    state: selected
  - text: Mr.
    state: hover
  - text: Ms.
    state: default
  - text: Mrs.
    state: default
  - text: Dr.
    state: default
  - text: Prof.
    state: default
  - text: Mx.
    state: default
dictionary_item_removed:
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][0]['sub_fields'][0]['value']: --None--
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][1]['placeholder']: Last
    Name
values_changed:
  root['content'][0]['description']:
    new_value: A 'New Lead' creation form is displayed in a modal dialog. The 'Salutation'
      dropdown menu has been opened, and the user is hovering over an option.
    old_value: A 'New Lead' creation form is displayed in a modal dialog over the
      main application view, which is dimmed in the background.
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][0]['sub_fields'][0]['state']:
    new_value: open
    old_value: focused
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][1]['label']:
    new_value: Website
    old_value: Last Name
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][1]['required']:
    new_value: false
    old_value: true
iterable_item_removed:
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][0]['sub_fields'][1]:
    component: text_input
    label: First Name
    placeholder: First Name
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][2]:
    component: text_input
    label: Company
    required: true
    value: ''
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][3]:
    component: text_input
    label: Title
    required: false
    value: ''
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][4]:
    component: text_input
    label: Website
    required: false
    value: ''
