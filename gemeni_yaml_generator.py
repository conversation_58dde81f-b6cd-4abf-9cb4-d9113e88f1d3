import os
import yaml
import time
import re
from pathlib import Path
import google.generativeai as genai
from typing import List
import PIL.Image

class SimpleScreenshotAnalyzer:
    def __init__(self, api_key: str = None):
        """Initialize with Google GenAI."""
        genai.configure(api_key=api_key or os.getenv('GOOGLE_API_KEY'))
        
        # Configure safety settings to be more permissive for screenshot analysis
        self.safety_settings = [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_NONE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_NONE"
            }
        ]
        
        # Configure generation settings for better reliability
        self.generation_config = {
            "temperature": 0.1,
            "top_p": 0.95,
            "top_k": 40,
            "max_output_tokens": 8192,
        }
        
        self.model = genai.GenerativeModel(
            'gemini-2.5-pro',
            safety_settings=self.safety_settings,
            generation_config=self.generation_config
        )
        
        self.chat = None  # For context tracking
        
        # Simple system prompt
        self.system_prompt = """Given a screenshot (frame), analyze and extract structured information with the following rules:

1. **Classify the frame into two regions:**
   - `browser_component`: Contains only the active tab title and the URL from the address bar.
   - `webpage`: The rendered website content inside the browser viewport.

2. **From the browser_component:**
   - Extract the **title of the active tab**.
   - Extract the **URL** visible in the address bar.

3. **From the webpage:**
   - Identify and classify visible subcomponents.

Return the analysis in valid YAML format only."""

    def extract_timestamp_from_filename(self, filename: str) -> str:
        """
        Extract timestamp from filename patterns like:
        - frame_0000_001.jpg -> returns "001"
        - frame_0001_012.jpg -> returns "012"
        - frame_0000.jpg -> returns "" (no timestamp)
        """
        # Pattern to match frame_XXXX_YYY.jpg or frame_XXXX_YYY_context_analysis.yaml
        pattern = r'frame_\d{4}_(\d{3})'
        match = re.search(pattern, filename)
        if match:
            return match.group(1)  # Return the timestamp part
        
        # Fallback: check if it's already processed file
        pattern_processed = r'frame_\d{4}_(\d{3})_.*\.yaml'
        match_processed = re.search(pattern_processed, filename)
        if match_processed:
            return match_processed.group(1)
        
        return ""  # No timestamp found

    def generate_output_filename(self, input_path: str, suffix: str = "context_analysis") -> str:
        """
        Generate output filename with timestamp.
        Examples:
        - frame_0000_001.jpg -> frame_0000_001_context_analysis.yaml
        - frame_0001.jpg -> frame_0001_context_analysis.yaml (no timestamp in input)
        """
        input_name = Path(input_path).stem  # Get filename without extension
        timestamp = self.extract_timestamp_from_filename(input_name)
        
        if timestamp:
            # Extract frame number (assuming frame_XXXX_YYY format)
            frame_match = re.search(r'frame_(\d{4})', input_name)
            if frame_match:
                frame_num = frame_match.group(1)
                return f"frame_{frame_num}_{timestamp}_{suffix}.yaml"
        
        # Fallback to original logic if no timestamp found
        return f"{input_name}_{suffix}.yaml"

    def handle_response_error(self, response, image_path: str) -> str:
        """Handle various response errors and return appropriate YAML."""
        try:
            # Check if response has candidates
            if not response.candidates:
                return f"# Error: No candidates returned for {Path(image_path).name}\nerror: 'No response candidates generated'"
            
            candidate = response.candidates[0]
            
            # Check finish reason
            finish_reason = candidate.finish_reason
            
            if finish_reason == 1:  # STOP
                # This should be normal, but text might be empty
                if hasattr(candidate.content, 'parts') and candidate.content.parts:
                    return candidate.content.parts[0].text
                else:
                    return f"# Error: Empty response for {Path(image_path).name}\nerror: 'Response generated but no text content'"
            elif finish_reason == 2:  # MAX_TOKENS
                return f"# Error: Max tokens reached for {Path(image_path).name}\nerror: 'Response truncated due to token limit'"
            elif finish_reason == 3:  # SAFETY
                return f"# Error: Safety filter triggered for {Path(image_path).name}\nerror: 'Content blocked by safety filters'"
            elif finish_reason == 4:  # RECITATION
                return f"# Error: Recitation filter triggered for {Path(image_path).name}\nerror: 'Content blocked due to recitation concerns'"
            else:
                return f"# Error: Unknown finish reason {finish_reason} for {Path(image_path).name}\nerror: 'Unexpected response termination'"
        
        except Exception as e:
            return f"# Error processing response for {Path(image_path).name}\nerror: {str(e)}"

    def analyze_screenshot(self, image_path: str, max_retries: int = 3) -> str:
        """Analyze single screenshot with retry logic."""
        for attempt in range(max_retries):
            try:
                image = PIL.Image.open(image_path)
                response = self.model.generate_content([self.system_prompt, image])
                
                # Handle response with error checking
                try:
                    result = response.text.strip()
                except Exception:
                    # Handle cases where response.text fails
                    result = self.handle_response_error(response, image_path)
                
                # Clean markdown and stray characters like backticks
                if result.startswith("```yaml"):
                    result = result.replace("```yaml", "").replace("```", "").strip()
                elif result.startswith("```"):
                    result = result.replace("```", "").strip()

                # Strip any leading backticks or junk
                result = result.lstrip("` \n\r\t")

                return result

                
                
            except Exception as e:
                print(f"Attempt {attempt + 1}/{max_retries} failed for {image_path}: {e}")
                if attempt < max_retries - 1:
                    # Exponential backoff: 2^attempt seconds
                    wait_time = 2 ** attempt
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                else:
                    return f"# Error analyzing {Path(image_path).name} after {max_retries} attempts\nerror: {str(e)}"

    def process_images_with_context(self, image_paths: List[str], output_folder: str = "yaml_outputs", isIntent: str = None):
        """Process images with context tracking."""
        # Import YamlDiffProcessor here to avoid circular imports
        from yamlDiff import YamlDiffProcessor
        
        Path(output_folder).mkdir(exist_ok=True)
        
        # Start chat for context with safety settings
        self.chat = self.model.start_chat()
        
        try:
            initial_response = self.chat.send_message(f"{self.system_prompt}\n\nI'll send you {len(image_paths)} screenshots. Remember context between them.")
            print("✓ Chat context initialized")
        except Exception as e:
            print(f"⚠️ Warning: Could not initialize chat context: {e}")
            # Propagate failure up so API can report it clearly
            return {"status": False, "error_stage": "gemini_chat_init", "error_message": str(e)}
        
        print(f"Processing {len(image_paths)} images with context...")
        
        for i, image_path in enumerate(image_paths, 1):
            print(f"[{i}/{len(image_paths)}] Processing: {Path(image_path).name}")
            
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    image = PIL.Image.open(image_path)
                    response = self.chat.send_message([f"Screenshot {i}:", image])
                    
                    if hasattr(response, 'usage_metadata') and response.usage_metadata:
                        print(f"Tokens used for image {i}: {response.usage_metadata}")
                    
                    # Handle response with error checking
                    try:
                        yaml_content = response.text.strip()
                    except Exception:
                        yaml_content = self.handle_response_error(response, image_path)
                    
                    # Clean response
                    # Clean markdown formatting
                    if yaml_content.startswith("```yaml"):
                        yaml_content = yaml_content.replace("```yaml", "").replace("```", "").strip()
                    elif yaml_content.startswith("```"):
                        yaml_content = yaml_content.replace("```", "").strip()

                    # Strip any leading backticks or junk
                    yaml_content = yaml_content.lstrip("` \n\r\t")

                    
                    # Generate output filename with timestamp
                    output_name = self.generate_output_filename(image_path, "context_analysis")
                    output_path = Path(output_folder) / output_name
                    
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(yaml_content)
                    
                    print(f"✓ Saved: {output_path}")
                    break  # Success, exit retry loop
                    
                except Exception as e:
                    print(f"Attempt {attempt + 1}/{max_retries} failed: {e}")
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt
                        print(f"Waiting {wait_time} seconds before retry...")
                        time.sleep(wait_time)
                    else:
                        # Return structured error to propagate up to API
                        return {"status": False, "error_stage": "gemini_image_analysis", "file": Path(image_path).name, "error_message": str(e)}
            
            # Add delay between images to avoid rate limiting
            if i < len(image_paths):
                time.sleep(2)  # 2-second delay between images
            
            # Add longer delay after every 20 images
            if i % 20 == 0 and i < len(image_paths):
                print("⏳ Waiting 1 minute before next batch...")
                time.sleep(60)
        
        print("🎉 Processing complete!")

        # Trigger yamlDiff.py after completion using class-based approach
        print("🔄 Triggering yamlDiff processor...")
        try:
            # Create YamlDiffProcessor instance and process
            yaml_processor = YamlDiffProcessor()
            json_result = yaml_processor.process_frame_diffs(output_folder,isIntent=isIntent)

            if json_result:
                print("✅ yamlDiff processing completed successfully!")
                return json_result
            else:
                print("⚠️ yamlDiff processing completed but no result returned")
                return {"status": False, "error_stage": "yaml_diff", "error_message": "No result returned from yamlDiff"}

        except Exception as e:
            print(f"❌ Error running yamlDiff processor: {e}")
            import traceback
            traceback.print_exc()
            return {"status": False, "error_stage": "yaml_diff", "error_message": str(e)}

    def process_images(self, image_paths: List[str], output_folder: str = "yaml_outputs"):
        """Process multiple images and save YAML files."""
        Path(output_folder).mkdir(exist_ok=True)
        
        print(f"Processing {len(image_paths)} images...")
        
        for i, image_path in enumerate(image_paths, 1):
            print(f"[{i}/{len(image_paths)}] Processing: {Path(image_path).name}")
            
            # Analyze screenshot with retry logic
            yaml_content = self.analyze_screenshot(image_path)
            
            # Generate output filename with timestamp
            output_name = self.generate_output_filename(image_path, "analysis")
            output_path = Path(output_folder) / output_name
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            
            if yaml_content.startswith("# Error"):
                print(f"❌ Error saved: {output_path}")
            else:
                print(f"✓ Saved: {output_path}")
            
            # Add delay between images
            if i < len(image_paths):
                time.sleep(2)
            
            # Add longer delay after every 20 images
            if i % 20 == 0 and i < len(image_paths):
                print("⏳ Waiting 1 minute before next batch...")
                time.sleep(60)
        
        print("🎉 Done!")

    def clear_context(self):
        """Clear the chat context and reset session."""
        self.chat = None
        print("Chat context cleared.")

    def auto_discover_images(self, folder_path: str, pattern: str = "*.jpg") -> List[str]:
        """
        Automatically discover all image files in a folder.
        
        Args:
            folder_path (str): Path to folder containing images
            pattern (str): File pattern to match (default: "*.jpg")
        
        Returns:
            List[str]: Sorted list of image file paths
        """
        import glob
        from pathlib import Path
        
        if not os.path.exists(folder_path):
            print(f"Folder not found: {folder_path}")
            return []
        
        # Find all matching files
        search_pattern = os.path.join(folder_path, pattern)
        image_paths = glob.glob(search_pattern)
        
        # Sort by filename to maintain order
        image_paths.sort()
        
        print(f"Found {len(image_paths)} images in {folder_path}")
        return image_paths

    def process_folder_with_context(self, input_folder: str, output_folder: str = None, pattern: str = "*.jpg", isIntent: str = None):
        """
        Process all images in a folder with context tracking.
        
        Args:
            input_folder (str): Folder containing images to process
            output_folder (str): Output folder for YAML files (defaults to input_folder + "_yaml_analysis")
            pattern (str): File pattern to match (default: "*.jpg")
        """
        # Auto-discover images
        image_paths = self.auto_discover_images(input_folder, pattern)
        
        if not image_paths:
            print("No images found to process!")
            return
        
        # Set default output folder
        if output_folder is None:
            output_folder = f"{input_folder}_yaml_analysis"
        
        print("Input image paths:", len(image_paths), "images")
        
        # Process with context
        return self.process_images_with_context(image_paths, output_folder ,isIntent)


if __name__ == "__main__":
    analyzer = SimpleScreenshotAnalyzer()
