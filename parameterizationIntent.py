import json
import os
from datetime import datetime
import re
import asyncio
import aiofiles
from typing import Dict, <PERSON><PERSON>, Any
import google.generativeai as genai
from playwright_Agent.agent import PlaywrightAgent
import sys


class AsyncVideoFrameAnalyzer:
    """An async class for analyzing video frame data and generating parameterized JSON outputs."""
    
    def __init__(self, api_key: str = None):
        """
        Initialize the AsyncVideoFrameAnalyzer.
        
        Args:
            api_key (str, optional): Google GenAI API key. If None, expects GOOGLE_API_KEY env var.
        """
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.model = None
        self.agent = PlaywrightAgent()
        
        if api_key:
            genai.configure(api_key=api_key)
        else:
            # Expects GOOGLE_API_KEY environment variable or manual configuration
            genai.configure(api_key=os.getenv('GOOGLE_API_KEY', 'GOOGLE_API_KEY'))
        
        self.model = genai.GenerativeModel('gemini-2.5-flash')
    
    async def load_json_file(self, file_path: str) -> Dict[str, Any]:
        """
        Asynchronously load JSON data from a file.
        
        Args:
            file_path (str): Path to the JSON file.
            
        Returns:
            dict: Loaded JSON data or error dict.
        """
        try:
            async with aiofiles.open(file_path, 'r') as file:
                content = await file.read()
                return json.loads(content)
        except FileNotFoundError:
            return {"error": f"File not found: {file_path}"}
        except json.JSONDecodeError as e:
            return {"error": f"Invalid JSON in file {file_path}: {e}"}
    
    async def save_json_file(self, data: Dict[str, Any], filename: str) -> Dict[str, Any]:
        """
        Asynchronously save data to a JSON file inside a timestamped folder.
        
        Args:
            data (dict): Data to save.
            filename (str): Name of the file.
            
        Returns:
            dict: Success status or error dict.
        """
        try:
            # Create folder with current timestamp
            folder_path = os.path.join(os.getcwd(), self.timestamp)
            os.makedirs(folder_path, exist_ok=True)
            
            # Full file path
            file_path = os.path.join(folder_path, filename)
            
            # Save JSON asynchronously
            async with aiofiles.open(file_path, 'w') as file:
                await file.write(json.dumps(data, indent=2))
            return {"success": f"File saved: {filename}"}
        except Exception as e:
            return {"error": f"Failed to save {filename}: {e}"}
    
    def extract_json_from_response(self, response_text: str) -> str:
        """
        Extract JSON from response text that may contain markdown code blocks.
        
        Args:
            response_text (str): Response text from the AI model.
            
        Returns:
            str: Extracted JSON text.
        """
        # Remove markdown code block markers
        # Look for ```json...``` or ```...``` patterns
        json_pattern = r'```(?:json)?\s*(.*?)\s*```'
        match = re.search(json_pattern, response_text, re.DOTALL)
        
        if match:
            json_text = match.group(1).strip()
        else:
            # If no code blocks found, use the entire response
            json_text = response_text.strip()
        
        return json_text
    
    async def process_with_genai(self, prompt: str) -> Dict[str, Any]:
        """
        Asynchronously process data with Google GenAI using the given prompt.
        
        Args:
            prompt (str): The prompt to send to the AI model.
            
        Returns:
            dict: Parsed JSON response from the model or error dict.
        """
        if not self.model:
            return {"error": "Model not initialized. Please provide a valid API key."}
        
        # Run the AI model call in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        
        try:
            response = await loop.run_in_executor(
                None, 
                self.model.generate_content, 
                prompt
            )
            print("Response received from AI model")
            
            # Extract JSON from the response text
            json_text = self.extract_json_from_response(response.text)
            
            # Parse JSON in executor to avoid blocking
            parsed_json = await loop.run_in_executor(
                None,
                json.loads,
                json_text
            )
            
            return parsed_json
            
        except json.JSONDecodeError as e:
            return {"error": f"Error parsing JSON: {e}"}
        except Exception as e:
            return {"error": f"Error in AI processing: {e}"}
    
    def generate_analysis_prompt(self, frame_data: Dict[str, Any]) -> str:
        """
        Generate the analysis prompt for video frame data.
        
        Args:
            frame_data (dict): Video frame data to analyze.
            
        Returns:
            str: Formatted analysis prompt.
        """
        return f"""
        You are an expert AI assistant specializing in process discovery and documentation from video feeds. Your primary function is to analyze user interaction recordings, which are composed of video frame data, to understand and document the actions performed by a user in a software application.

        Based on this video frame data, analyze the user's actions and generate a structured JSON output.

        Video Frame Data:
        {json.dumps(frame_data, indent=2)}

        **Core Tasks:**

        1. **Identify User Actions**: Analyze the sequence of UI interactions. Pay attention to:
        - Mouse movements and position changes
        - Navigation events (URL changes)
        - UI element interactions (clicks, typing, selections)
        - Tab switches and page transitions
        - Input field interactions (text input, dropdown selections, checkbox/radio button selections)

        2. **Capture Input Values**: When users enter data into input fields, dropdowns, or make selections, capture the exact values they provided.

        3. **Infer Intent**: Determine the user's overall goal based on the sequence of actions.

        4. **Generate Step-by-Step Guide**: Produce clear, human-readable steps that include specific values entered by the user.

        5. **Normalize Page URLs**: When generating the `page_url` field in the output, ensure all URLs are complete and include the correct protocol (`http` or `https`). If any `address_bar` values in the video frame data are missing the protocol (e.g., just a domain or path), infer the correct protocol dynamically by checking other `address_bar` entries with the same host that do include the protocol. Use the protocol from any matching full address to normalize all related entries.

        **Output Format:**

        Return ONLY a valid JSON object with this exact structure (no markdown code blocks, no extra text):

        {{
        "intent": "A high-level description of the user's overall goal",
        "action_summary": "A single, concise sentence summarizing the entire sequence of actions",
        "steps": [
            {{
            "step_number": 1,
            "action": "Clear description of the user's action including specific values entered",
            "details": {{
                "target_element": "UI element interacted with",
                "input_value": "Actual value entered by user (if applicable)",
                "cursor_position": [x, y],
                "page_url": "URL when action occurred (must include correct protocol)"
            }}
            }}
        ]
        }}
        """
    
    def generate_parameterization_prompt(self, result_json: Dict[str, Any]) -> str:
        """
        Generate the parameterization prompt for the result JSON.
        
        Args:
            result_json (dict): The result JSON to parameterize.
            
        Returns:
            str: Formatted parameterization prompt.
        """
        return f"""
        You are a JSON templating assistant. Your job: take a valid JSON document as input and replace specific *data values* with parameter placeholders (using `{{name}}` mustache-style). Do NOT change the JSON structure (keys, arrays, numeric types), only replace string values that match the rules below. Output must be valid JSON and nothing else.

        INPUT
        - You will be given one JSON object as input (the full object will be provided after this instruction).
        - Example fields to parameterize: emails, passwords, visible user inputs (brand names, product types, model names, statuses), page URLs, notification texts, and any user-supplied strings inside `action` or `details`. Cursor positions, element ids, step_number (integers) should be preserved unless explicitly requested.

        REPLACEMENT RULES (apply in this priority)
        1. **Email replacement**
        - Replace any string matching an email regex with `{{email}}`.
        - Regex: `\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{{2,}}\b`.
        - If multiple *distinct* emails appear, name them `{{email1}}`, `{{email2}}`, ... in order of appearance and reflect that in the parameters mapping.

        2. **Password replacement**
        - Replace any password value that is explicitly a user password with `{{password}}`.
        - If multiple passwords present, name them `{{password1}}`, `{{password2}}`, ...
        - If masked (e.g., `****`), leave unchanged.

        3. **User input / dropdown / text field values — CONTEXT-AWARE NAMING**
        - Identify the related UI element from `details.target_element` or `action` text.
        - Create the placeholder name from the element label or name in lower_snake_case.
            Examples:
            - `"Dropdown: Brand"` → `{{brand}}`
            - `"Dropdown: Product Type"` → `{{product_type}}`
            - `"Text Input: Model Name"` → `{{model_name}}`
            - `"Dropdown: Status"` → `{{status}}`
            - `"Button: Submit"` → `{{submit_button_label}}`
        - If the same element name appears multiple times with different values, suffix with `_1`, `_2` as needed.

        4. **Page URL replacement**
        - Replace any full URL string starting with `http://` or `https://` with `{{page_url}}`.
        - If multiple different URLs appear, use `{{page_url1}}`, `{{page_url2}}`, ...

        5. **Notification / success text**
        - Replace notification-like messages with `{{notification}}` (or `{{notification1}}` if multiple).
        - Keep names descriptive when possible (e.g., `{{equipment_creation_success}}` if the context is about creating equipment).

        6. **General rule for other visible user-supplied strings**
        - For any other literal string that is clearly user-supplied, base the placeholder name on the UI element name if available, otherwise use a descriptive short name.
        - Always prefer meaningful placeholder names tied to UI context over generic numbered names.

        NAMING / CONSISTENCY
        - Always use lower_snake_case inside `{{}}`.
        - Reuse the same placeholder for identical exact string values (case-sensitive).
        - Number placeholders only when the same field appears multiple times with different values.
        - Avoid meaningless names like `brand_name2`; always derive from the UI element name or label.

        OUTPUT FORMAT (exact)
        Return a JSON object with two top-level fields:
        1. `"parameterized_json"` — the original JSON but with replacements made.
        2. `"parameters"` — an object mapping placeholder names to original values.

        DO NOT OUTPUT ANY EXTRA TEXT. Return valid JSON only.

        PROCESS THE FOLLOWING JSON INPUT NOW. Produce only the two-field JSON described above.
        """ + json.dumps(result_json)

    
    async def analyze_video_frames(self, frame_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Asynchronously analyze video frame data and generate structured JSON output.
        
        Args:
            frame_data (dict): Video frame data to analyze.
            
        Returns:
            dict: Analysis result JSON.
        """
        analysis_prompt = self.generate_analysis_prompt(frame_data)
        return await self.process_with_genai(analysis_prompt)
    
    async def parameterize_result(self, result_json: Dict[str, Any]) -> Dict[str, Any]:
        """
        Asynchronously parameterize the analysis result JSON.
        
        Args:
            result_json (dict): The analysis result to parameterize.
            
        Returns:
            dict: Success result with (parameterized_json, parameters) or error dict.
        """
        parameterization_prompt = self.generate_parameterization_prompt(result_json)
        parameterized_result = await self.process_with_genai(parameterization_prompt)
        
        if "error" in parameterized_result:
            return parameterized_result
        
        parameterized_json = parameterized_result.get('parameterized_json', {})
        parameters = parameterized_result.get('parameters', {})
        
        return (parameterized_json, parameters)
    
    async def process_video_analysis(self, input_json_path: str, isIntent: str = None) -> Dict[str, Any]:
            """
            Complete async video analysis pipeline: load data, analyze, parameterize, and save results.
            
            Args:
                input_json_path (str): Path to input JSON file containing video frame data.
                isIntent (str): Optional parameter to control processing flow
                
            Returns:
                dict: Success result with data or error dict.
            """
            print("Starting video analysis pipeline...")
            
            # Load the raw JSON data
            print("Loading frame data...")
            frame_data = await self.load_json_file(input_json_path)
            if "error" in frame_data:
                return frame_data
            
            # Analyze video frames
            print("Analyzing video frames...")
            result_json = await self.analyze_video_frames(frame_data)
            if "error" in result_json:
                return result_json
            
            # Save result and start parameterization concurrently
            print("Saving initial results and parameterizing...")
            save_result_task = asyncio.create_task(
                self.save_json_file(result_json, 'result.json')
            )
            
            parameterize_task = asyncio.create_task(
                self.parameterize_result(result_json)
            )
            
            # Wait for both tasks to complete
            save_result = await save_result_task
            parameterize_result = await parameterize_task
            
            if "error" in save_result:
                return save_result
            
            if "error" in parameterize_result:
                return parameterize_result
            
            parameterized_json, parameters = parameterize_result
            
            # Save parameterized results concurrently
            print("Saving parameterized results...")
            save_tasks = [
                asyncio.create_task(self.save_json_file(parameterized_json, 'parameterized_result.json')),
                asyncio.create_task(self.save_json_file(parameters, 'parameters.json'))
            ]
            
            save_results = await asyncio.gather(*save_tasks)
            for save_result in save_results:
                if "error" in save_result:
                    return save_result
            
            print("Processing complete. Results saved to 'result.json', 'parameterized_result.json', and 'parameters.json'")
            print("Processing complete. Results saved to 'result.json'", isIntent)
            
            # if isIntent == "active":
            #     print("comes play")
            #     # Return just the intent analysis without invoking the agent
            #     try:
            #         # result_json is already a dict from the AI model, no need to parse
            #         if isinstance(result_json, dict):
            #             return result_json
            #         elif isinstance(result_json, str):
            #             return json.loads(result_json)
            #         else:
            #             return {"status": False, "error_stage": "intent_parsing", "error_message": "Unexpected result_json type"}
            #     except json.JSONDecodeError:
            #         return {"status": False, "error_stage": "intent_parsing", "error_message": "Failed to parse intent response as JSON"}
            # print("comes after check")
            # Send original response to agent (already async)
            if isinstance(result_json, dict):
                finalresp = await self.agent.invoke_user_input(f"""{json.dumps(result_json)}""")
            else:
                finalresp = await self.agent.invoke_user_input(f"""{result_json}""")
            print("fina",finalresp)
            # If the agent returned a structured error, propagate it
            if isinstance(finalresp, dict) and not finalresp.get("status", True):
                return {"status": False, "error_stage": "playwright_agent", "error_message": finalresp.get("data")}

            # Ensure finalresp is JSON if it's a string
            while isinstance(finalresp, str):
                try:
                    finalresp = json.loads(finalresp)
                    break
                except json.JSONDecodeError:
                    break

            return finalresp

        
            # return {
            #     "success": True,
            #     "result_json": result_json,
            #     "parameterized_json": parameterized_json,
            #     "parameters": parameters
            # }
        
    async def process_multiple_files(self, input_paths: list) -> list:
        """
        Process multiple video analysis files concurrently.
        
        Args:
            input_paths (list): List of paths to input JSON files.
            
        Returns:
            list: List of tuples containing (result_json, parameterized_json, parameters) for each file.
        """
        print(f"Processing {len(input_paths)} files concurrently...")
        
        tasks = [
            asyncio.create_task(self.process_video_analysis(path))
            for path in input_paths
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Error processing file {input_paths[i]}: {result}")
            else:
                successful_results.append(result)
        
        print(f"Successfully processed {len(successful_results)} out of {len(input_paths)} files")
        return successful_results


async def main(input_json_path,isIntent=None):
    """Main async function demonstrating usage of the AsyncVideoFrameAnalyzer class."""
    # # Path to input JSON file
    # if not input_json_path:
    #         if len(sys.argv) < 2:
    #             print("Usage: python parameterization.py <input_json_path>")
    #             return None
    #         input_json_path = sys.argv[1]

    print("call comes >>>>")
    # Initialize analyzer
    analyzer = AsyncVideoFrameAnalyzer()
    print("pathy",input_json_path)
    # Process single file
    result = await analyzer.process_video_analysis(input_json_path,isIntent)
    
    if result.get("success"):
        print("Analysis completed successfully!")
        return result["result_json"]
    else:
        print(f"Analysis failed: {result.get('error', 'Unknown error')}")
        return result


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python parameterizationIntent.py <input_json_path>")
        sys.exit(1)  # Exit with error code

    path_arg = sys.argv[1]
    asyncio.run(main(path_arg))