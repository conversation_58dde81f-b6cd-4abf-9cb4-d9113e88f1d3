#!/usr/bin/env python3
"""
Simple Video Frame Processor with API Support
Extract frames from videos and identify duplicate frames.
Now supports receiving video files via API form data using FastAPI.
"""
from gemeni_yaml_generator import SimpleScreenshotAnalyzer
import glob
import datetime
import cv2
import os
import sys
from typing import List, Tuple, Optional
import shutil
import aiofiles
import asyncio
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

app = FastAPI(title="Video Frame Processor", description="Extract frames from videos and identify duplicate frames")

# Configuration
UPLOAD_FOLDER = 'uploaded_videos'
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm'}

# Ensure upload directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

# Create dynamic output folder name with timestamp (for uniqueness)
output_folder_name = f"finalFrames_{timestamp}"

class ProcessVideoResponse(BaseModel):
    status: str
    message: str
    output_folder: str
    unique_frames_count: int
    analysis_completed: bool
    analysis_result: Optional[dict] = None
    error_stage: Optional[str] = None
    error_message: Optional[str] = None

def allowed_file(filename: str) -> bool:
    """Check if the uploaded file has an allowed extension."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

async def save_uploaded_video(file: UploadFile) -> str:
    """
    Save uploaded video file to local directory.
    
    Args:
        file: UploadFile object from FastAPI
        
    Returns:
        str: Path to saved video file
    """
    if file and allowed_file(file.filename):
        # Create secure filename with timestamp
        filename = file.filename
        name, ext = os.path.splitext(filename)
        timestamped_filename = f"{name}_{timestamp}{ext}"
        
        # Save to current directory's upload folder
        current_dir = os.path.dirname(os.path.abspath(__file__))
        video_path = os.path.join(current_dir, UPLOAD_FOLDER, timestamped_filename)
        
        # Read and save file asynchronously
        async with aiofiles.open(video_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
            
        print(f"Video saved to: {video_path}")
        return video_path
    else:
        raise ValueError("Invalid file type. Allowed types: " + ", ".join(ALLOWED_EXTENSIONS))

@app.post('/process_video', response_model=ProcessVideoResponse)
async def api_process_video(
    video: UploadFile = File(...),
    fps_interval: Optional[float] = Form(1.0),
    duplicate_threshold: Optional[int] = Form(50000),
    auto_analyze: Optional[bool] = Form(True),
    isIntent: Optional[str] = Form(None) 
):
    """
    API endpoint to process video from form data.
    
    Parameters:
    - video: video file (required)
    - fps_interval: float, default 1.0
    - duplicate_threshold: int, default 50000
    - auto_analyze: bool, default True
    """
    try:
        # Validate video file
        if not video.filename:
            raise HTTPException(status_code=400, detail="No file selected")
        
        # Save uploaded video
        video_path = await save_uploaded_video(video)
        
        # Process the video
        result = await process_video_with_unique_frames(
            video_path=video_path,
            output_folder=output_folder_name,
            fps_interval=fps_interval,
            duplicate_threshold=duplicate_threshold,
            auto_analyze=auto_analyze,
            isIntent=isIntent
        )
        
        # Clean up uploaded video file (optional)
        if os.path.exists(video_path):
            os.remove(video_path)
            print(f"Cleaned up uploaded video: {video_path}")
        
        # Build response based on downstream result
        if isinstance(result, dict) and result.get("analysis_result") and isinstance(result["analysis_result"], dict) and not result["analysis_result"].get("status", True):
            # Downstream component returned an error structure
            error_info = result["analysis_result"]
            response_data = ProcessVideoResponse(
                status="error",
                message="Processing failed",
                output_folder=output_folder_name,
                unique_frames_count=len(result.get("unique_frames", [])) if isinstance(result, dict) else 0,
                analysis_completed=False,
                error_stage=error_info.get("error_stage"),
                error_message=error_info.get("error_message"),
                intent_only=(isIntent == "active")
            )
        else:
            response_data = ProcessVideoResponse(
                status="success",
                message="Video processed successfully",
                output_folder=output_folder_name,
                unique_frames_count=len(result.get("unique_frames", [])) if isinstance(result, dict) else len(result),
                analysis_completed=bool(result.get("analysis_result")) if isinstance(result, dict) else False,
                analysis_result=result.get("analysis_result") if isinstance(result, dict) else None,
                intent_only=(isIntent == "active") 
            )

        return response_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def split_video_to_images(video_path: str, output_folder: str = "myFrames", fps_interval: float = 1.0, remove_duplicates: bool = True, duplicate_threshold: int = 50000) -> List[str]:
    """
    Extract frames from a video file and optionally remove duplicates automatically.
    Filenames use frame numbers only: frame_XXXX.jpg where XXXX is frame number.

    Args:
        video_path (str): Path to the input video file
        output_folder (str): Directory to save extracted frames
        fps_interval (float): Extract one frame every N seconds
        remove_duplicates (bool): If True, automatically check and remove duplicate frames after extraction
        duplicate_threshold (int): Pixel difference threshold for duplicates

    Returns:
        List[str]: List of paths to final frame images (unique if remove_duplicates=True)
    """
    if not os.path.exists(video_path):
        raise FileNotFoundError(f"Video file not found: {video_path}")
    
    os.makedirs(output_folder, exist_ok=True)
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise ValueError(f"Unable to open video file: {video_path}")
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    print(f"Video FPS: {fps}")
    frame_interval = int(fps * fps_interval)
    
    frame_count = 0
    saved_count = 0
    frame_paths = []
    
    # Step 1: Extract frames
    print("Extracting frames...")
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        if frame_count % frame_interval == 0:
            # Create filename with frame number only: frame_XXXX.jpg
            frame_filename = f"frame_{saved_count:04d}.jpg"
            frame_path = os.path.join(output_folder, frame_filename)

            if cv2.imwrite(frame_path, frame):
                frame_paths.append(frame_path)
                saved_count += 1
                # Calculate timestamp for display purposes only
                timestamp_seconds = int(frame_count / fps)
                print(f"Saved: {frame_filename} (at {timestamp_seconds}s)")
        
        frame_count += 1
    
    cap.release()
    print(f"Extracted {saved_count} frames to '{output_folder}/'")
    
    # Step 2: Remove duplicates if requested
    if remove_duplicates and frame_paths:
        print("Checking for duplicate frames...")
        duplicates, unique_paths = find_duplicate_images(
            frame_paths, 
            threshold=duplicate_threshold, 
            delete_duplicates=True
        )
        
        if duplicates:
            print(f"Removed {len(duplicates)} duplicate frames")
            return unique_paths
        else:
            print("No duplicate frames found")
    
    return frame_paths


def find_duplicate_images(image_paths: List[str], output_folder: str = None, threshold: int = 50000, delete_duplicates: bool = False) -> Tuple[List[Tuple[str, str]], List[str]]:
    """
    Detect duplicate or very similar images from a list of image paths and save unique images to a folder.
    
    Args:
        image_paths (List[str]): List of paths to image files
        output_folder (str): Directory to save unique frames (optional)
        threshold (int): Pixel difference threshold for duplicates
        delete_duplicates (bool): If True, delete duplicate images from original folder
    
    Returns:
        Tuple[List[Tuple[str, str]], List[str]]: List of duplicate image path pairs and list of unique image paths
    """
    if not image_paths:
        return [], []
    
    if output_folder:
        os.makedirs(output_folder, exist_ok=True)
    
    duplicates = []
    processed_images = []
    unique_paths = []
    
    print(f"Processing {len(image_paths)} images for duplicates...")
    
    for i, current_path in enumerate(image_paths):
        if not os.path.exists(current_path):
            print(f"File not found: {current_path}")
            continue
        
        current_img = cv2.imread(current_path)
        if current_img is None:
            print(f"Failed to read image: {current_path}")
            continue
        
        current_gray = cv2.cvtColor(current_img, cv2.COLOR_BGR2GRAY)
        is_duplicate = False
        
        # Check against all previous processed images
        for prev_path, prev_gray in processed_images:
            diff = cv2.absdiff(current_gray, prev_gray)
            non_zero_count = cv2.countNonZero(diff)
            
            if non_zero_count < threshold:
                duplicates.append((prev_path, current_path))
                print(f"Duplicate found: {os.path.basename(current_path)} is similar to {os.path.basename(prev_path)}")
                is_duplicate = True
                break
        
        if not is_duplicate:
            processed_images.append((current_path, current_gray))
            unique_paths.append(current_path)
            # Copy to output folder if specified
            if output_folder:
                filename = os.path.basename(current_path)
                output_path = os.path.join(output_folder, filename)
                shutil.copy(current_path, output_path)
        
        # Progress indicator
        if (i + 1) % 10 == 0:
            print(f"Processed {i + 1}/{len(image_paths)} images...")
    
    if duplicates:
        print(f"Found {len(duplicates)} duplicate pairs")
    
    if output_folder:
        print(f"Saved {len(unique_paths)} unique frames to '{output_folder}/'")
    
    # Optionally delete duplicate images
    if delete_duplicates:
        duplicate_paths = set(curr_path for _, curr_path in duplicates)
        for path in duplicate_paths:
            if os.path.exists(path):
                os.remove(path)
                print(f"Deleted duplicate: {os.path.basename(path)}")
    
    return duplicates, unique_paths


async def process_video_with_unique_frames(video_path: str, output_folder: str = "BTQFrames", fps_interval: float = 1.0, duplicate_threshold: int = 50000, auto_analyze: bool = True,isIntent: str = None) -> dict:
    """
    Complete workflow: Extract frames from video and automatically remove duplicates.
    Then optionally trigger YAML analysis.
    """
    temp_folder = f"{output_folder}_temp"

    try:
        loop = asyncio.get_event_loop()

        # Step 1: Extract all frames to temporary folder (offloaded to executor)
        print("=== Step 1: Extracting frames ===")
        all_frames = await loop.run_in_executor(
            None,
            split_video_to_images,
            video_path,
            temp_folder,
            fps_interval,
            False  # remove_duplicates
        )

        # Step 2: Find and remove duplicates, save unique frames to final folder (offloaded)
        print("\n=== Step 2: Processing duplicates ===")
        duplicates, unique_frames = await loop.run_in_executor(
            None,
            find_duplicate_images,
            all_frames,
            output_folder,
            duplicate_threshold,
            False  # delete_duplicates
        )

        # Step 3: Clean up temporary folder
        print(f"\n=== Step 3: Cleaning up ===")
        if os.path.exists(temp_folder):
            await loop.run_in_executor(None, shutil.rmtree, temp_folder)
            print(f"Removed temporary folder: {temp_folder}")

        print(f"\n=== Summary ===")
        print(f"Total frames extracted: {len(all_frames)}")
        print(f"Duplicate frames found: {len(duplicates)}")
        print(f"Unique frames saved: {len(unique_frames)}")
        print(f"Final output folder: {output_folder}")

        # Step 4: Auto-trigger YAML analysis if requested
        if auto_analyze and unique_frames:
            print(f"\n=== Step 4: Starting YAML Analysis ===")
            try:
                analyzer = SimpleScreenshotAnalyzer()
                current_dir = os.path.dirname(os.path.abspath(__file__))
                input_folder_local = os.path.join(current_dir, output_folder)

                # Run the analyzer in a thread pool to avoid blocking
                json_result = await loop.run_in_executor(
                    None,
                    analyzer.process_folder_with_context,
                    input_folder_local,
                    f"contextual_analysis_{timestamp}",
                    "*.jpg",
                    isIntent
                )

                print("✅ YAML analysis completed successfully!")

                # Return the JSON result along with unique_frames
                if isinstance(json_result, dict) and json_result.get("status") is False:
                    # Propagate the error structure
                    return {"unique_frames": unique_frames, "analysis_result": json_result}
                elif json_result:
                    return {"unique_frames": unique_frames, "analysis_result": json_result}
                else:
                    return {"unique_frames": unique_frames, "analysis_result": {"status": False, "error_stage": "gemeni_yaml_generator", "error_message": "No result returned"}}

            except Exception as e:
                print(f"❌ YAML analysis failed: {e}")
                print("You can run the analysis manually later.")
                return {"unique_frames": unique_frames, "analysis_result": {"status": False, "error_stage": "gemini_yaml_generator", "error_message": str(e)}}

        return {"unique_frames": unique_frames, "analysis_result": None}

    except Exception as e:
        # Clean up on error
        if os.path.exists(temp_folder):
            try:
                await asyncio.get_event_loop().run_in_executor(None, shutil.rmtree, temp_folder)
            except Exception:
                pass
        raise e

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Video Frame Processor API is running", "endpoints": ["/process_video"]}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    print("Video Frame Processor with FastAPI Support")
    print("Starting FastAPI server...")
    print("API endpoint: POST /process_video")
    print("Expected form data: video file + optional parameters")
    print("Health check: GET /")
    
    # Run the FastAPI server
    uvicorn.run(app, host='0.0.0.0', port=5000, log_level="info")