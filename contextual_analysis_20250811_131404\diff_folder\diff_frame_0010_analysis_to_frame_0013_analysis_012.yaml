values_changed:
  root['browser_component']['url']:
    new_value: https://ability-power-35702.lightning.force.com/lightning/o/Lead/new?count=1&nooverride=1&useRecordTypeCheck=1&navigationLocation=LIST_VIEW&uid=175457197816162033&backgrou...
    old_value: https://ability-power-35702.lightning.force.com/lightning/o/Lead/list?filterName=Recent
  root['content'][0]:
    new_value:
      component: layered_view
      description: A modal dialog is displayed over the main application, which is
        dimmed in the background. The modal is in a loading state.
      background_layer:
        component: application_view
        platform: Salesforce Lightning
        state: inactive_dimmed
        view_name: Leads List - Recently Viewed
        elements_visible:
        - sidebar
        - header
        - list_view_header
        - data_table
      foreground_layer:
        component: modal_dialog
        state: loading
        header:
          actions:
          - type: close_button
            icon: x
        body:
          component: skeleton_loader
          description: The content for the new record form is loading, indicated by
            grey placeholder bars.
          layout:
            type: form_skeleton
            columns: 2
            sections:
            - fields:
              - type: placeholder
              - type: placeholder
            - fields:
              - type: placeholder
              - type: placeholder
            - fields:
              - type: placeholder
              - type: placeholder
            - fields:
              - type: placeholder
              - type: placeholder
    old_value:
      component: application_layout
      platform: Salesforce Lightning
      sidebar:
        type: vertical_navigation
        items:
        - text: Home
          icon: home
          state: inactive
        - text: Contacts
          icon: contacts
          state: inactive
        - text: Accounts
          icon: accounts
          state: inactive
        - text: Sales
          icon: sales
          state: active
        - text: Service
          icon: service
          state: inactive
        - text: Marketing
          icon: marketing
          state: inactive
        - text: Commerce
          icon: commerce
          state: inactive
        - text: Generative Canvas
          icon: generative_canvas
          state: inactive
        - text: Your Account
          icon: account
          state: inactive
      main_view:
        header:
          component: app_header
          app_name: Sales
          navigation_bar:
            component: tab_navigation
            active_tab: Leads
            tabs:
            - Leads
            - Contacts
            - Accounts
            - Opportunities
            - Products
            - Price Books
            - Calendar
            - Analytics
          global_search:
            placeholder: Search...
          utility_bar:
          - type: icon_button
            label: Help
          - type: icon_button
            label: Settings
          - type: icon_button
            label: Notifications
          - type: user_profile_icon
        content_area:
          component: list_view
          object_type: Leads
          view_name: Recently Viewed
          status: loading
          header_actions:
          - type: button
            text: New
          - type: button
            text: Import
          - type: button
            text: Add to Campaign
          - type: button
            text: Send Email
          - type: button
            text: Change Owner
          list_controls:
            search:
              placeholder: Search this list...
            actions:
            - settings
            - display_as_table
            - refresh
            - edit
            - chart
            - filter
          content:
            type: loading_indicator
            style: blue_dots_spinner
          footer:
            component: utility_bar
            items:
            - text: To Do List
