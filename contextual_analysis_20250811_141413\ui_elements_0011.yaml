browser_component:
  tab_title: Recently Viewed | Leads | Salesforce
  url: ability-power-35702.lightning.force.com/lightning/o/Lead/list?filterName=Recent
webpage:
  subcomponents:
  - class: header
    subcomponents:
    - class: logo
      details: Salesforce logo
    - class: banner
      text: Days left in Starter trial
      subcomponents:
      - class: badge
        text: 17
      - class: button
        text: Buy Now
    - class: search_bar
      placeholder: Search...
    - class: icon_button
      label: Help
    - class: icon_button
      label: Settings
    - class: icon_button
      label: Notifications
    - class: user_profile_icon
      details: A bear mascot icon representing the user profile.
  - class: navigation_bar
    subcomponents:
    - class: text
      text: Sales
    - class: tab_list
      tabs:
      - text: Leads
        active: true
      - text: Contacts
        active: false
      - text: Accounts
        active: false
      - text: Opportunities
        active: false
      - text: Products
        active: false
      - text: Price Books
        active: false
      - text: Calendar
        active: false
      - text: Analytics
        active: false
  - class: sidebar
    position: left
    state: expanded
    subcomponents:
    - class: menu
      items:
      - text: Home
        icon: home
      - text: Contacts
        icon: contacts
      - text: Accounts
        icon: building
      - text: Sales
        icon: chart
        active: true
      - text: Service
        icon: heart
      - text: Marketing
        icon: search
      - text: Commerce
        icon: cart
      - text: Generative Canvas
        icon: grid
      - text: Your Account
        icon: building
  - class: main_content
    subcomponents:
    - class: heading
      text: Leads
      level: 2
    - class: view_selector
      current_view: Recently Viewed
    - class: button_group
      buttons:
      - text: New
      - text: Import
      - text: Add to Campaign
      - text: Send Email
      - text: Change Owner
    - class: text
      text: 2 items • Updated a few seconds ago
    - class: search_bar
      placeholder: Search this list...
    - class: icon_button_group
      buttons:
      - label: Settings
      - label: Display as Table
      - label: Refresh
      - label: Edit
      - label: Chart
      - label: Filter
    - class: table
      headers:
      - Name
      - Title
      - Company
      - Phone
      - Email
      - Lead Status
      - Owner Alias
      rows:
      - Name: Dhanush KV
        Title: null
        Company: CCS
        Phone: null
        Email: null
        Lead Status: New
        Owner Alias: JJ
      - Name: Test Lead
        Title: null
        Company: CCS
        Lead Status: New
        Owner Alias: JJ
  - class: footer
    subcomponents:
    - class: collapsible_panel
      title: To Do List
      state: collapsed