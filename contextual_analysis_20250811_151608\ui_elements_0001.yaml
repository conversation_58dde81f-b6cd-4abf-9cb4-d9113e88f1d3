browser_component:
  active_tab_title: AMC-Admin
  url: http://*************/equipment
webpage:
  subcomponents:
    - component: header
      subcomponents:
        - component: search_input
          placeholder: Search
        - component: user_profile
          user_name: <PERSON><PERSON>
          user_role: Admin
          subcomponents:
            - component: notification_icon
              value: 6
    - component: sidebar_navigation
      subcomponents:
        - component: logo
          text: Logo
        - component: navigation_menu
          menu_title: SERVICE CONTRACT
          items:
            - item: Dashboard
            - item: Items
            - item: Customer
            - item: Equipment
              state: active
            - item: Proposal & Pricing
            - item: Contracts
            - item: Tickets
            - item: Employee
            - item: Organization Set...
        - component: footer_menu
          items:
            - item: Settings
            - item: Logout
    - component: main_content
      subcomponents:
        - component: equipment_list
          title: Manage Equipment
          subcomponents:
            - component: table
              headers:
                - MODEL NAME
                - PRODUCT TYPE
                - BRAND NAME
                - STATUS
                - ACTION
              rows: []
        - component: add_equipment_form
          title: Add Equipment
          subcomponents:
            - component: dropdown
              label: Brand
              value: Brand
            - component: dropdown
              label: Product Type
              value: Product Type
            - component: text_input
              label: Model Name
              placeholder: Model Name
            - component: dropdown
              label: Status
              value: Select Status
            - component: button
              text: Cancel
            - component: button
              text: Create
              state: primary