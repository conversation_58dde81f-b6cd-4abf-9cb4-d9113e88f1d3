{
    "intent": "The user's overall goal is to log into the \"{{ application_name }}\" application, navigate to the master data settings, and add a new vehicle brand.",
    "action_summary": "The user successfully logged into the \"{{ application_name }}\" application, navigated through the settings to the Vehicle Brand management section, and added a new vehicle brand named \"{{ vehicle_brand_name }}\".",
    "steps": [
        {
            "step_number": 1,
            "action": "User focused on the browser's address bar.",
            "details": {
                "target_element": "Address and search bar (id: {{ address_bar_id }})",
                "cursor_position": [{{ cursor_x_1 }}, {{ cursor_y_1 }}],
                "page_url": "{{ base_url }}/"
            }
        },
        {
            "step_number": 9,
            "action": "User typed \"{{ vehicle_brand_name }}\" into the 'Brand Name' input field.",
            "details": {
                "target_element": "Brand Name (id: {{ brand_name_input_id }})",
                "cursor_position": [{{ cursor_x_9 }}, {{ cursor_y_9 }}],
                "page_url": "{{ base_url }}/master/vehiclebrand/{{ vehiclebrand_id }}"
            }
        }
    ]
}
