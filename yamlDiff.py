import yaml
import os
import glob
import re
import sys
from deepdiff import DeepDiff
import shutil
import asyncio 

class YamlDiffProcessor:
    """Class to handle YAML diff processing operations."""
    
    def __init__(self):
        """Initialize the YAML diff processor."""
        pass

    def load_yaml_file(self, path):
        """Load a YAML file and return its content."""
        with open(path, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)

    def save_yaml_file(self, data, path):
        """Save data to a YAML file."""
        with open(path, 'w', encoding='utf-8') as file:
            yaml.dump(data, file, allow_unicode=True, sort_keys=False)

    def extract_timestamp_from_filename(self, filename):
        """
        Extract timestamp from filename patterns like:
        - frame_0000_001_context_analysis.yaml -> returns "001"
        - frame_0001_012_context_analysis.yaml -> returns "012"
        - frame_0000_context_analysis.yaml -> returns "000" (default)
        """
        # Pattern to match frame_XXXX_YYY_context_analysis.yaml
        pattern = r'frame_\d{4}_(\d{3})_.*\.yaml'
        match = re.search(pattern, filename)
        if match:
            return match.group(1)  # Return the timestamp part
        
        # If no timestamp found, return default "000"
        return "000"

    def extract_frame_info(self, filename):
        """
        Extract frame number and timestamp from filename.
        Returns: (frame_number, timestamp)
        Examples:
        - frame_0000_001_context_analysis.yaml -> ("0000", "001")
        - frame_0001_context_analysis.yaml -> ("0001", "000")
        """
        # First try to extract frame number and timestamp
        pattern = r'frame_(\d{4})_(\d{3})_.*\.yaml'
        match = re.search(pattern, filename)
        if match:
            return match.group(1), match.group(2)
        
        # Fallback: extract just frame number, use "000" as default timestamp
        pattern_fallback = r'frame_(\d{4})_.*\.yaml'
        match_fallback = re.search(pattern_fallback, filename)
        if match_fallback:
            return match_fallback.group(1), "000"
        
        # Last resort
        return "0000", "000"

    def compare_yaml(self, file1, file2, output_diff):
        """Compare two YAML files and save the differences."""
        yaml1 = self.load_yaml_file(file1)
        yaml2 = self.load_yaml_file(file2)
        
        # Use DeepDiff to compare deeply
        diff = DeepDiff(yaml1, yaml2, verbose_level=2)
        
        # Clean output to make it YAML-friendly
        diff_dict = diff.to_dict()
        
        # Save the diff as YAML
        self.save_yaml_file(diff_dict, output_diff)
        print(f"Differences saved in '{output_diff}'")
    
    async def process_frame_diffs_async(self, yaml_folder: str = "yaml_outputs", output_filename: str = None):
        """Async version of process_frame_diffs"""
        # Use run_in_executor for CPU-bound operations
        import asyncio
        loop = asyncio.get_event_loop()
        
        return await loop.run_in_executor(
            None,
            self.process_frame_diffs,
            yaml_folder,
            output_filename
        )

    def process_frame_diffs(self, input_folder, frame_pattern="frame_*.yaml", isIntent=None):
        """
        Process all frame files sequentially and create diffs between consecutive frames
        
        Args:
            input_folder: Path to folder containing the frame YAML files
            frame_pattern: Pattern to match frame files (default: "frame_*.yaml")
             isIntent: Optional flag to indicate if we should only process intent
        
        Returns:
            JSON result from context generator
        """
        # Create diff_folder if it doesn't exist
        diff_folder = os.path.join(input_folder, "diff_folder")
        os.makedirs(diff_folder, exist_ok=True)
        
        # Find all frame files and sort them
        frame_files = glob.glob(os.path.join(input_folder, frame_pattern))
        
        # Debug: Print all found files before sorting
        print(f"Files found with pattern '{frame_pattern}':")
        for f in frame_files:
            print(f"  - {os.path.basename(f)}")
        
        # Sort files naturally (handles numbers correctly)
        frame_files.sort(key=lambda x: int(''.join(filter(str.isdigit, os.path.basename(x)))))
        
        # Debug: Print sorted files
        print(f"\nSorted order:")
        for i, f in enumerate(frame_files):
            print(f"  {i}: {os.path.basename(f)}")
        
        if len(frame_files) < 2:
            print(f"Found only {len(frame_files)} frame files. Need at least 2 files to create diffs.")
            return None
        
        print(f"\nTotal files found: {len(frame_files)}")
        
        # ✅ FIRST: Copy the first frame to diff_folder with custom "_000" suffix
        print(f"\n{'='*50}")
        print("Step 1: Copying first frame to output folder with custom _000 suffix...")
        print('='*50)
        
        first_frame = frame_files[0]
        first_frame_name = os.path.basename(first_frame)
        first_frame_base = os.path.splitext(first_frame_name)[0]
        
        # Extract frame info and create custom name with _000
        frame_num, _ = self.extract_frame_info(first_frame_name)
        
        # Create new name: base_frame_XXXX_000_context_analysis_000.yaml
        if "context_analysis" in first_frame_base:
            first_frame_renamed = f"base_frame_{frame_num}_000_context_analysis_000.yaml"
        else:
            first_frame_renamed = f"base_frame_{frame_num}_000_analysis_000.yaml"
        
        first_frame_dest = os.path.join(diff_folder, first_frame_renamed)
        shutil.copy2(first_frame, first_frame_dest)
        print(f"✅ FIRST - Copied first frame: {first_frame_name} → {first_frame_renamed}")
        
        # ✅ SECOND: Process consecutive frame pairs sequentially
        print(f"\n{'='*50}")
        print("Step 2: Processing diff files sequentially...")
        print('='*50)
        
        for i in range(len(frame_files) - 1):
            frame1_path = frame_files[i]
            frame2_path = frame_files[i + 1]
            
            # Extract frame info from both files
            frame1_name = os.path.basename(frame1_path)
            frame2_name = os.path.basename(frame2_path)
            
            frame1_num, frame1_timestamp = self.extract_frame_info(frame1_name)
            frame2_num, frame2_timestamp = self.extract_frame_info(frame2_name)
            
            # Create output diff filename with timestamp from second frame
            if "context_analysis" in frame1_name:
                diff_filename = f"diff_frame_{frame1_num}_context_analysis_to_frame_{frame2_num}_context_analysis_{frame2_timestamp}.yaml"
            else:
                diff_filename = f"diff_frame_{frame1_num}_analysis_to_frame_{frame2_num}_analysis_{frame2_timestamp}.yaml"
            
            diff_output_path = os.path.join(diff_folder, diff_filename)
            
            print(f"\nProcessing diff {i+1}: {frame1_name} vs {frame2_name}")
            print(f"Output filename: {diff_filename}")
            print(f"Second frame timestamp: {frame2_timestamp}")
            
            try:
                self.compare_yaml(frame1_path, frame2_path, diff_output_path)
            except Exception as e:
                print(f"Error processing {frame1_name} vs {frame2_name}: {str(e)}")
                # Propagate error with context to upstream caller
                raise RuntimeError(f"yaml_diff_failure for {frame1_name} vs {frame2_name}: {e}")
        
        print(f"\n✅ All processing complete! Check the '{diff_folder}' folder for results.")
        print(f"Output structure:")
        print(f"1. base_frame_{frame_num}_000_context_analysis_000.yaml (original first frame with custom _000)")
        print(f"2. diff files with second frame timestamps:")
        
        # Show example of output files
        for i in range(min(3, len(frame_files) - 1)):  # Show first 3 examples
            frame1_name = os.path.basename(frame_files[i])
            frame2_name = os.path.basename(frame_files[i + 1])
            frame1_num, _ = self.extract_frame_info(frame1_name)
            frame2_num, frame2_timestamp = self.extract_frame_info(frame2_name)
            example_diff = f"diff_frame_{frame1_num}_context_analysis_to_frame_{frame2_num}_context_analysis_{frame2_timestamp}.yaml"
            print(f"   - {example_diff}")
        
        # Trigger context generator using class-based approach
        result = self.trigger_context_generator(diff_folder, isIntent)
        return result

    def trigger_context_generator(self, diff_folder_path, isIntent=None):
        """
        Trigger the context_generator.py using class-based approach
        Returns the JSON result from context_generator
        isIntent: Optional flag to indicate if we should only process intent
        """
        try:
            # Import and use context generator as a class
            from context_generator import ContextGenerator  # Assuming it exists as a class
            
            print(f"\n{'='*50}")
            print("Triggering context generator...")
            print('='*50)

            context_gen = ContextGenerator()
            
            # Create context generator instance and process
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            json_result = loop.run_until_complete(
                context_gen.main(diff_folder_path, isIntent)
            )
            loop.close()
            
            if json_result:
                print("✅ Context generator completed successfully!")
                return json_result
            else:
                print("⚠️ Context generator completed but returned no result")
                return None
                
        except ImportError:
            print("❌ context_generator.py not found or doesn't have ContextGenerator class")
            return None
        except Exception as e:
            print(f"❌ Error running context generator: {str(e)}")
            import traceback
            traceback.print_exc()
            return None


# === Standalone Usage ===
if __name__ == "__main__":
    try:
        print("\n" + "="*60 + "\n")
        
        if len(sys.argv) > 1:
            input_folder_path = sys.argv[1]
            print(f"📁 Processing folder from command line: {input_folder_path}")
        else:
            # Fallback to hardcoded path if no argument provided
            input_folder_path = r"E:\loveable_AI\bunch\contextual_analysis_20250808_123802"
            print(f"📁 Using default folder: {input_folder_path}")
        
        # Verify folder exists
        if not os.path.exists(input_folder_path):
            print(f"❌ Error: Folder '{input_folder_path}' does not exist!")
            sys.exit(1)
        
        print("\n" + "="*60 + "\n")
        
        # Try different patterns to see which one matches your files
        patterns_to_try = [
            "frame_*_*_context_analysis.yaml",  # New timestamped pattern
            "frame_*_context_analysis.yaml",    # Original pattern
            "frame_*.yaml",                     # Simpler pattern
            "*context_analysis.yaml"            # Even broader pattern
        ]
        
        print("Testing different file patterns:")
        for pattern in patterns_to_try:
            test_files = glob.glob(os.path.join(input_folder_path, pattern))
            print(f"Pattern '{pattern}': {len(test_files)} files found")
            if test_files:
                print(f"  Sample files: {[os.path.basename(f) for f in test_files[:3]]}")
        
        # Use the pattern that works for your timestamped files
        file_pattern = "frame_*_*_context_analysis.yaml"  # Updated for timestamped files
        
        print(f"\n{'='*50}")
        print("Starting processing with pattern:", file_pattern)
        print('='*50)
        
        # Create processor instance and run
        processor = YamlDiffProcessor()
        json_result = processor.process_frame_diffs(input_folder_path, file_pattern)
        
        # Output the JSON result
        if json_result:
            import json
            print(f"\n{'='*50}")
            print("FINAL JSON RESULT:")
            print('='*50)
            print(json.dumps(json_result, indent=2))
        else:
            print("No JSON result received.")
            
    except Exception as e:
        print(f"CRITICAL ERROR in yamlDiff.py: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
