dictionary_item_added:
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][1]['placeholder']: Last
    Name
values_changed:
  root['content'][0]['description']:
    new_value: A 'New Lead' creation form is displayed in a modal dialog. The 'Salutation'
      has been set to 'Mr.' and the 'First Name' field is currently focused for input.
    old_value: A 'New Lead' creation form is displayed in a modal dialog. The 'Salutation'
      dropdown menu has been opened, and the user is hovering over an option.
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][0]['sub_fields'][0]:
    new_value:
      component: dropdown
      label: Salutation
      value: Mr.
      actions:
      - type: icon_button
        label: revert_selection
    old_value:
      component: dropdown
      label: Salutation
      state: open
      current_value: --None--
      options:
      - text: --None--
        state: selected
      - text: Mr.
        state: hover
      - text: Ms.
        state: default
      - text: Mrs.
        state: default
      - text: Dr.
        state: default
      - text: Prof.
        state: default
      - text: Mx.
        state: default
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][1]['label']:
    new_value: Last Name
    old_value: Website
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][1]['required']:
    new_value: true
    old_value: false
iterable_item_added:
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][0]['sub_fields'][1]:
    component: text_input
    label: First Name
    placeholder: First Name
    state: focused
    value: ''
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][2]:
    component: text_input
    label: Company
    required: true
    value: ''
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][3]:
    component: text_input
    label: Title
    required: false
    value: ''
  root['content'][0]['foreground_layer']['body']['sections'][0]['fields'][4]:
    component: text_input
    label: Website
    required: false
    value: ''
