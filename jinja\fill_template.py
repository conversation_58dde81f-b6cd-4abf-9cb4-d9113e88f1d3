from jinja2 import Environment, FileSystemLoader
import json

# Load Jinja2 environment (current directory)
env = Environment(loader=FileSystemLoader('.'))

# Load the template JSON file
template = env.get_template('template.json')

# Parameters to fill in
params = {
    "application_name": "Car Garage SAD",
    "vehicle_brand_name": "Benz",
    "address_bar_id": "url_bar",
    "cursor_x_1": 9000,
    "cursor_y_1": 88,
    "base_url": "http://maga.com",
    "brand_name_input_id": "bab<PERSON>",
    "cursor_x_9": 435,
    "cursor_y_9": 556,
    "vehiclebrand_id": 1
}

# Render template with parameters
rendered_json_str = template.render(params)

# Parse rendered string to ensure it's valid JSON
rendered_json = json.loads(rendered_json_str)

# Save to local file
with open("filled.json", "w") as f:
    json.dump(rendered_json, f, indent=4)

print("✅ JSON file 'filled.json' created successfully!")
