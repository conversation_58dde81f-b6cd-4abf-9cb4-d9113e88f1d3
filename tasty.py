import google.generativeai as genai

# Configure the API key
genai.configure(api_key="AIzaSyDFznf3ccZgiJTk2avzv1BHZq2joX0VeJU")

# Initialize the model
model = genai.GenerativeModel('gemini-2.5-flash')

# Define instruction and description
instruction = "You are a helpful coding assistant. Provide clear and concise explanations."
description = "Write a simple Python function that calculates the area of a circle given its radius."

# Combine instruction and description into a prompt
prompt = f"Instruction: {instruction}\n\nTask: {description}"

# Generate content using the model
try:
    response = model.generate_content(prompt)
    print("Generated Response:")
    print("-" * 50)
    print(response.text)
    
except Exception as e:
    print(f"Error generating content: {e}")