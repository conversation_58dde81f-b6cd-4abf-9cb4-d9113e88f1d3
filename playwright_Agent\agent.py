# playwright_runner.py

import asyncio
import sys
import os
from google.adk.agents.llm_agent import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.mcp_tool.mcp_toolset import (
    MCPToolset,
    StdioConnectionParams,
    StdioServerParameters,
)
from google.adk.sessions import InMemorySessionService
from google.adk.runners import Runner
from google.genai import types
from pydantic import BaseModel, Field
from google.genai.types import Schema


sys.path.append(os.path.dirname(os.path.abspath(__file__)))
# from custom_adk_patches import CustomMCPToolset

# class PlaywrightOutput(BaseModel):
#     page_title: str = Field(description="The title of the page")
#     screenshot_url: str = Field(description="URL to screenshot or image")
#     extracted_text: str = Field(description="Text you extracted from the page")

class PlaywrightAgent:
    def __init__(self, model="gemini-2.0-flash"):
        self.model = model
        self.agent = self._create_agent()
        self.session_service = InMemorySessionService()
        self.runner = Runner(agent=self.agent, app_name="playwright_app", session_service=self.session_service)
       
    def _create_agent(self):
        return Agent(
            name="playwright_agent",
            model=self.model,
            description="Browser automation agent with Playwright",
            instruction="""
            You are a browser automation agent powered by Playwright. Your job is to interact with web pages just like a human would, using Playwright commands. You can open URLs, click buttons, fill input fields, take screenshots, wait for elements, and extract content.
            """,
            tools=[
                MCPToolset(
                connection_params=StdioConnectionParams(
                        server_params=StdioServerParameters(
                        command="npx",
                        args=["@playwright/mcp@latest"]
                    ),
                    timeout =300.0
                 )             
                )
            ],
        )

    async def invoke_user_input(self, user_input: str):
        await self.session_service.create_session(app_name="playwright_app", user_id="user1", session_id="sess1")
        content = types.Content(parts=[types.Part(text=user_input)], role="user")
        events_async = self.runner.run_async(
            user_id="user1", session_id="sess1", new_message=content
        )

        try:
            async for event in events_async:
                if event.is_final_response() and event.content.parts:
                    return {
                        "status": True,
                        "message": "data retrieved successfully",
                        "data": event.content.parts[0].text
                    }
        except Exception as e:
            print("⚠️ Tool error occurred:", e)
            return {
                "status": False,
                "message": "Failed when using MCP",
                "data": str(e)
            }
        finally:
            await events_async.aclose()

# # Optional local test
# if __name__ == "__main__":
#     user_instruction = (
#         "Open the browser and navigate to http://3.110.125.122/, wait for the Car Garage login page to load, "
#         "click the `User ID` field, enter <EMAIL>, ensure the password auto-fills with ZZUBQYYV3K, "
#         "verify both fields are correct, then click `Login` and wait for the login process to complete, "
#         "ensuring a `Login Successful` notification appears and the Job Card List page is loaded."
#     )

#     asyncio.run(PlaywrightAgent().invoke_user_input(user_instruction))
