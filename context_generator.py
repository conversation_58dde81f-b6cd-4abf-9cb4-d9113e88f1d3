import os
import yaml
import json
import google.generativeai as genai
from pathlib import Path
from datetime import datetime

class ContextGenerator:
    def __init__(self, api_key="AIzaSyDhnI4SD3TdF-rUCq8UxtbNDOiDA-Np7Pw"):
        """Initialize the Context Generator with API key"""
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.5-flash')
        
        # Your prompt template
        self.PROMPT_TEMPLATE = """
You are an expert UI analyst.

Given a YAML structure that describes changes in a user interface (UI) or browser state, generate a **clear and concise natural language summary** of the modifications. Each change includes an `old_value` and a `new_value`.

Please follow these instructions:

1. Interpret and describe each change using human-readable sentences.
2. Group related changes together (e.g., changes to the same UI element).
3. Use terms like "The label was changed from X to Y" or "The size of the element was adjusted".
4. Mention specific UI elements using their type and label (if available).
5. For mouse cursor movements or interactions, clearly describe the action, such as "The mouse cursor moved from position (x, y) to (x, y)" or "The target element under the cursor changed from X to Y".

Now analyze the following YAML and describe the changes in natural language:



```yaml
{yaml_content}
```
"""



    def save_results_to_json(self, results_array, output_file=None):
        """Save results array to JSON file"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"yaml_analysis_results_{timestamp}.json"

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results_array, f, indent=2, ensure_ascii=False)
            print(f"\nResults saved to: {output_file}")
            return output_file
        except Exception as e:
            print(f"Error saving to JSON: {str(e)}")
            return None

    def get_analysis_strings(self, results_array):
        """Extract just the AI analysis strings from results"""
        return [result['ai_analysis'] for result in results_array]

    def get_file_names(self, results_array):
        """Extract just the file names from results"""
        return [result['file_details']['file_name'] for result in results_array]

    def get_analysis_by_filename(self, results_array):
        """Get dictionary mapping filenames to their analysis"""
        return {result['file_details']['file_name']: result['ai_analysis'] for result in results_array}

    def process_yaml_files(self, folder_path):
        """Process all YAML files in the given folder"""
        results_array = []
        
        # Get all YAML files from the folder
        yaml_files = list(Path(folder_path).glob("*.yaml")) + list(Path(folder_path).glob("*.yml"))

        for yaml_file in yaml_files:
            try:
                # Read YAML file
                with open(yaml_file, 'r', encoding='utf-8') as file:
                    yaml_content = file.read()

                # Create prompt with YAML content
                prompt = self.PROMPT_TEMPLATE.format(yaml_content=yaml_content)

                # Generate response using Gemini
                response = self.model.generate_content(prompt)

                # Extract last 3 digits from filename for video_timestamp
                filename = yaml_file.name
                filename_without_ext = filename.rsplit('.', 1)[0]
                video_timestamp = filename_without_ext[-3:]

                # Create result object
                result_obj = {
                    "file_details": {
                        "file_name": yaml_file.name,
                        "file_path": str(yaml_file),
                        "yaml_content": yaml_content
                    },
                    "ai_analysis": response.text.strip(),
                    "video_timestamp": video_timestamp
                }

                results_array.append(result_obj)
                print(f"Processed: {yaml_file.name} - Timestamp: {video_timestamp}")

            except Exception as e:
                print(f"Error processing {yaml_file}: {str(e)}")
                continue

        return results_array

    async def main(self, folder_path,isIntent=None):
            """
            Main processing function that processes YAML files and triggers intent analysis
            Returns the final JSON result from intent generator
            """
            # print(f"Processing folder: {folder_path}")

            # # Verify folder exists
            if not os.path.exists(folder_path):
                print(f"Error: Folder does not exist: {folder_path}")
                return None

            # Process all YAML files
            results = self.process_yaml_files(folder_path)

            # Save results to JSON file
            json_file = self.save_results_to_json(results)
            # json_file = r"E:\loveable_AI\bunch\yaml_analysis_results_20250810_224933.json"

            # Trigger intent generator and capture result
            if json_file:
                print(f"\n{'='*50}")
                print("AUTOMATICALLY TRIGGERING INTENT ANALYSIS...")
                print(f"{'='*50}")

                try:
                    # Import and use intent generator as a class
                    # from intent_generator import IntentGenerator

                    # intent_gen = IntentGenerator()
                    # intent_result = intent_gen.analyze_video_frames_sync(json_file , isIntent)

                    from parameterizationIntent import main

                    intent_result = await  main(json_file , isIntent)
                    print("intent_result",intent_result)
                    if intent_result:
                        print("FINAL RESULT FROM INTENT GENERATOR:")
                        print(json.dumps(intent_result, indent=2))
                        return intent_result
                    else:
                        print("Intent generator returned no result")
                        return {"status": False, "error_stage": "intent_generator", "error_message": "No result returned by intent generator"}

                except Exception as e:
                    print(f"Error running intent_generator: {e}")
                    import traceback
                    traceback.print_exc()
                    return {"status": False, "error_stage": "intent_generator", "error_message": str(e)}

            return {"status": False, "error_stage": "context_generator", "error_message": "No JSON file produced"}

# Backward compatibility for standalone usage




# # Main execution
import asyncio

if __name__ == "__main__":
        async def run_main():
            context_gen = ContextGenerator()
            result = await context_gen.main(r"E:\loveable_AI\bunch\contextual_analysis_20250811_001309\diff_folder", "active")
            if result:
                print("\nProcess completed successfully!")
            else:
                print("\nProcess failed!")

        asyncio.run(run_main())



